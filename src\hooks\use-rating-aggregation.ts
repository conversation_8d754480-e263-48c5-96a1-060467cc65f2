
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';

export interface RatingStats {
  averageRating: number;
  totalReviews: number;
  ratingDistribution: {
    1: number;
    2: number;
    3: number;
    4: number;
    5: number;
  };
}

export function useProviderRatingStats(providerId: string) {
  return useQuery({
    queryKey: ['provider-rating-stats', providerId],
    queryFn: async (): Promise<RatingStats> => {
      const { data, error } = await supabase
        .from('reviews')
        .select('rating')
        .eq('provider_id', providerId)
        .eq('is_public', true);

      if (error) throw error;

      if (!data || data.length === 0) {
        return {
          averageRating: 0,
          totalReviews: 0,
          ratingDistribution: { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 }
        };
      }

      const ratings = data.map(r => r.rating);
      const averageRating = ratings.reduce((sum, rating) => sum + rating, 0) / ratings.length;
      
      const distribution = { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 };
      ratings.forEach(rating => {
        distribution[rating as keyof typeof distribution]++;
      });

      return {
        averageRating,
        totalReviews: ratings.length,
        ratingDistribution: distribution
      };
    },
    enabled: !!providerId
  });
}

export function useGigRatingStats(gigId: string) {
  return useQuery({
    queryKey: ['gig-rating-stats', gigId],
    queryFn: async (): Promise<RatingStats> => {
      const { data, error } = await supabase
        .from('reviews')
        .select('rating')
        .eq('gig_id', gigId)
        .eq('is_public', true);

      if (error) throw error;

      if (!data || data.length === 0) {
        return {
          averageRating: 0,
          totalReviews: 0,
          ratingDistribution: { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 }
        };
      }

      const ratings = data.map(r => r.rating);
      const averageRating = ratings.reduce((sum, rating) => sum + rating, 0) / ratings.length;
      
      const distribution = { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 };
      ratings.forEach(rating => {
        distribution[rating as keyof typeof distribution]++;
      });

      return {
        averageRating,
        totalReviews: ratings.length,
        ratingDistribution: distribution
      };
    },
    enabled: !!gigId
  });
}

export function useAutoUpdateRatings() {
  const updateRatings = async (providerId: string, gigId?: string) => {
    // This would trigger database functions to update aggregated ratings
    // For now, we'll just log the update
    console.log('Updating ratings for provider:', providerId, 'gig:', gigId);
  };

  return { updateRatings };
}
