import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from './use-auth';

export interface Review {
  id: string;
  gig_id?: string;
  reviewer_id: string;
  provider_id: string;
  rating: number;
  title?: string;
  comment?: string;
  is_public: boolean;
  created_at: string;
  updated_at: string;
  reviewer?: {
    id: string;
    display_name?: string;
    username?: string;
    avatar_url?: string;
  };
  provider?: {
    id: string;
    name: string;
  };
  gig?: {
    id: string;
    title: string;
  };
  booking?: {
    id: string;
    title: string;
  };
}

export interface CreateReviewData {
  booking_id?: string;
  gig_id?: string;
  provider_id: string;
  rating: number;
  title?: string;
  comment?: string;
  is_public: boolean;
}

export interface UpdateReviewData {
  rating: number;
  title?: string;
  comment?: string;
  is_public: boolean;
}

export function useProviderReviews(providerId: string, limit: number = 10) {
  return useQuery({
    queryKey: ['provider-reviews', providerId, limit],
    queryFn: async (): Promise<Review[]> => {
      const { data, error } = await supabase
        .from('reviews')
        .select('*')
        .eq('provider_id', providerId)
        .eq('is_public', true)
        .order('created_at', { ascending: false })
        .limit(limit);

      if (error) throw error;
      return (data || []).map(review => ({
        ...review,
        reviewer: undefined,
        provider: undefined,
        gig: undefined
      }));
    },
    enabled: !!providerId
  });
}

export function useCreateReview() {
  const { user } = useAuth();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: CreateReviewData): Promise<Review> => {
      if (!user) throw new Error('User not authenticated');

      const { data: review, error } = await supabase
        .from('reviews')
        .insert({
          reviewer_id: user.id,
          ...data
        })
        .select()
        .single();

      if (error) throw error;
      return review;
    },
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ 
        queryKey: ['provider-reviews', variables.provider_id] 
      });
    }
  });
}

export function useUpdateReview() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ 
      reviewId, 
      updateData 
    }: { 
      reviewId: string; 
      updateData: UpdateReviewData;
    }): Promise<Review> => {
      const { data: review, error } = await supabase
        .from('reviews')
        .update(updateData)
        .eq('id', reviewId)
        .select()
        .single();

      if (error) throw error;
      return review;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['provider-reviews'] });
    }
  });
}

export function useDeleteReview() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (reviewId: string): Promise<void> => {
      const { error } = await supabase
        .from('reviews')
        .delete()
        .eq('id', reviewId);

      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['provider-reviews'] });
    }
  });
}
